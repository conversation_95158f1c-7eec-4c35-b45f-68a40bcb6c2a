import os, strutils

const pluginsDir = "plugins"
const registryFile = "core/plugin_registry.nim"

proc generatePluginRegistry() =
  var content = """# 自动生成的插件注册文件
# 这个文件会自动包含所有插件

import utils/log

# 导入所有插件
"""
  
  var importedPlugins: seq[string] = @[]
  
  if dirExists(pluginsDir):
    for (kind, path) in walkDir(pluginsDir):
      let filename = extractFilename(path)
      
      if kind == pcFile and filename.endsWith(".nim"):
        # .nim 文件插件
        let moduleName = filename.replace(".nim", "")
        content.add("import ../plugins/" & moduleName & "\n")
        importedPlugins.add(moduleName)
        
      elif kind == pcDir:
        # 目录插件，查找 init.nim
        let initPath = path / "init.nim"
        if fileExists(initPath):
          content.add("import ../plugins/" & filename & "/init\n")
          importedPlugins.add(filename & "/init")
  
  content.add("\nproc loadAllPlugins*() =\n")
  if importedPlugins.len > 0:
    content.add("  logInfo(\"--- 所有插件已自动加载，共 " & $importedPlugins.len & " 个插件 ---\")\n")
  else:
    content.add("  logInfo(\"--- 未发现任何插件 ---\")\n")
  
  writeFile(registryFile, content)
  echo "插件注册文件已生成: ", registryFile
  echo "包含插件: ", importedPlugins

when isMainModule:
  generatePluginRegistry()
