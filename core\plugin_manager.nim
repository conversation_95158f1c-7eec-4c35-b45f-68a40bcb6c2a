import utils/log

type
  PluginHandler* = proc(msg: string): tuple[handled: bool,
      reply: string] {.gcsafe.}

var handlers*: seq[PluginHandler] = @[]

proc registerPlugin*(handler: PluginHandler) =
  handlers.add(handler)
  logInfo("插件已注册")

proc processMessage*(msg: string): string =
  for handler in handlers:
    let response = handler(msg)
    if response.handled:
      return response.reply
  ""









