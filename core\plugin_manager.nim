import utils/log
import os, strutils, sequtils

type
  PluginHandler* = proc(msg: string): tuple[handled: bool,
      reply: string] {.gcsafe.}
  PluginManager* = ref object
    handlers*: seq[PluginHandler]

var pluginManager* {.threadvar.}: PluginManager
const pluginsDir = "plugins"

proc loadPlugin(pluginPath, displayName: string): bool =
  try:
    when defined(nimscript):
      include pluginPath
    else:
      # 编译时包含插件
      logInfo("已加载插件: " & displayName)
    true
  except:
    logError("加载插件失败: " & displayName)
    false

proc loadPlugins*() =
  logInfo("--- 开始加载插件 ---")

  if not dirExists(pluginsDir):
    logError("无法读取插件目录 '" & pluginsDir & "'")
    logError("--- 插件加载失败 ---")
    return

  let files = toSeq(walkDir(pluginsDir))
  if files.len == 0:
    logInfo("插件目录为空，未加载任何插件")
    return

  var loadedCount = 0

  for (kind, path) in files:
    let filename = extractFilename(path)

    if kind == pcFile and filename.endsWith(".nim"):
      # .nim 文件插件
      let modulePath = pluginsDir / filename.replace(".nim", "")
      if loadPlugin(modulePath, filename):
        inc loadedCount
    elif kind == pcDir:
      # 目录插件，查找 init.nim
      let initPath = path / "init.nim"
      if fileExists(initPath):
        let modulePath = pluginsDir / filename / "init"
        if loadPlugin(modulePath, filename & "/init.nim"):
          inc loadedCount
      else:
        logInfo("目录插件缺少入口文件: " & filename & "/init.nim")

  logInfo("--- 插件加载完毕，共加载 " & $loadedCount & " 个插件 ---")

proc initPluginManager*() =
  if pluginManager == nil:
    pluginManager = PluginManager(handlers: @[])
    loadPlugins()

proc registerPlugin*(handler: PluginHandler) =
  if pluginManager == nil:
    pluginManager = PluginManager(handlers: @[])
  pluginManager.handlers.add(handler)
  logInfo("插件已注册")

proc processMessage*(msg: string): string {.gcsafe.} =
  if pluginManager == nil:
    return ""
  for handler in pluginManager.handlers:
    let response = handler(msg)
    if response.handled:
      return response.reply
  ""









