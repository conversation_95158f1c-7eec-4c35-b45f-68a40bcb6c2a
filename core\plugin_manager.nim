import utils/log
import macros

type
  PluginHandler* = proc(msg: string): tuple[handled: bool,
      reply: string] {.gcsafe.}
  PluginManager* = ref object
    handlers*: seq[PluginHandler]

var pluginManager* {.threadvar.}: PluginManager

# 自动注册插件的宏
macro autoRegisterPlugin*(handler: untyped): untyped =
  result = quote do:
    # 确保插件管理器已初始化
    if pluginManager == nil:
      pluginManager = PluginManager(handlers: @[])

    # 注册处理器
    pluginManager.handlers.add(`handler`)
    logInfo("插件已自动注册: " & astToStr(`handler`))

# 自动初始化插件管理器
proc ensurePluginManager() =
  if pluginManager == nil:
    pluginManager = PluginManager(handlers: @[])
    logInfo("插件管理器已初始化")

# 在模块加载时自动初始化
ensurePluginManager()

proc registerPlugin*(handler: PluginHandler) =
  ensurePluginManager()
  pluginManager.handlers.add(handler)
  logInfo("插件已注册")

proc processMessage*(msg: string): string {.gcsafe.} =
  ensurePluginManager()
  for handler in pluginManager.handlers:
    let response = handler(msg)
    if response.handled:
      return response.reply
  ""









