import ../core/utils/log
import ../core/plugin_manager
import json

proc helloHandler(msg: string): tuple[handled: bool, reply: string] =
  try:
    let jsonMsg = parseJson(msg)
    if jsonMsg.hasKey("raw_message"):
      let rawMsg = jsonMsg["raw_message"].getStr()
      if rawMsg == "你好":
        logInfo("处理你好消息")
        let response = %*{
          "action": "send_group_msg",
          "params": {
            "group_id": jsonMsg["group_id"],
            "message": "你好！"
          }
        }
        return (true, $response)
  except:
    discard
  (false, "")

# 注册插件
registerPlugin(helloHandler)


